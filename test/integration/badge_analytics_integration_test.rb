# frozen_string_literal: true

require 'test_helper'

class BadgeAnalyticsIntegrationTest < ActionDispatch::IntegrationTest
  def setup
    # Disable color contrast validation for integration tests
    ENV['VALIDATE_BADGE_COLOR_CONTRAST'] = 'false'

    # Create admin user with superadmin role
    @admin =
      User.create!(
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
        onboarding_completed: true,
        scout_signup_completed: true,
      )

    # Create superadmin role if it doesn't exist
    superadmin_role = Role.find_or_create_by(name: 'superadmin')
    UserRole.find_or_create_by(user: @admin, role: superadmin_role)

    # Create test badge types
    @badge_type1 =
      BadgeType.create!(
        name: 'Expert Badge',
        description: 'For expert users',
        background_color: '#3B82F6',
        text_color: '#FFFFFF',
        icon: 'star',
        priority: 1,
        active: true,
      )

    @badge_type2 =
      BadgeType.create!(
        name: 'Contributor Badge',
        description: 'For active contributors',
        background_color: '#10B981',
        text_color: '#FFFFFF',
        icon: 'heart',
        priority: 2,
        active: true,
      )

    # Create test users
    @user1 =
      User.create!(
        name: 'Test User 1',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
      )

    @user2 =
      User.create!(
        name: 'Test User 2',
        email: '<EMAIL>',
        password: 'password123456',
        verified: true,
      )

    # Create comprehensive test data
    create_comprehensive_test_data
  end

  test 'complete badge analytics workflow' do
    # Sign in as admin
    sign_in_as_admin

    # Navigate to admin dashboard
    get super_admin_admin_dashboard_path
    assert_response :success

    # Check that badge analytics link is present
    assert_select 'a[href=?]', super_admin_badge_analytics_path

    # Navigate to badge analytics
    get super_admin_badge_analytics_path
    assert_response :success

    # Check page structure
    assert_select 'h1', text: /Badge Analytics/
    assert_select '.overview-metrics'
    assert_select '.badge-performance'
    assert_select '.user-engagement'
    assert_select '.click-analytics'

    # Check filter form
    assert_select 'form' do
      assert_select 'select[name="date_range"]'
      assert_select 'select[name="badge_type_id"]'
      assert_select 'button[type="submit"]', text: /Filter/
    end
  end

  test 'filtering functionality works end-to-end' do
    sign_in_as_admin

    # Test date range filtering
    get super_admin_badge_analytics_path, params: { date_range: '7' }
    assert_response :success
    assert_select 'select[name="date_range"] option[selected][value="7"]'

    # Test badge type filtering
    get super_admin_badge_analytics_path,
        params: {
          badge_type_id: @badge_type1.id,
        }
    assert_response :success
    assert_select "select[name=\"badge_type_id\"] option[selected][value=\"#{@badge_type1.id}\"]"

    # Test combined filtering
    get super_admin_badge_analytics_path,
        params: {
          date_range: '14',
          badge_type_id: @badge_type2.id,
        }
    assert_response :success
    assert_select 'select[name="date_range"] option[selected][value="14"]'
    assert_select "select[name=\"badge_type_id\"] option[selected][value=\"#{@badge_type2.id}\"]"
  end

  test 'CSV export functionality works end-to-end' do
    sign_in_as_admin

    # Test CSV export with default parameters
    get export_super_admin_badge_analytics_path, params: { format: :csv }
    assert_response :success
    assert_equal 'text/csv', response.content_type

    csv_content = response.body
    lines = csv_content.split("\n")

    # Check CSV structure
    assert lines.length >= 2 # Header + at least one data row

    header = lines.first
    assert_includes header, 'Badge Type'
    assert_includes header, 'Total Assignments'
    assert_includes header, 'Total Clicks'
    assert_includes header, 'Total Views'

    # Check that badge data is included
    data_lines = lines[1..-1].reject(&:blank?)
    assert data_lines.any? { |line| line.include?(@badge_type1.name) }
    assert data_lines.any? { |line| line.include?(@badge_type2.name) }
  end

  test 'analytics data accuracy across different time periods' do
    sign_in_as_admin

    # Test 7-day analytics
    get super_admin_badge_analytics_path, params: { date_range: '7' }
    assert_response :success
    analytics_7d = assigns(:analytics)

    # Test 30-day analytics
    get super_admin_badge_analytics_path, params: { date_range: '30' }
    assert_response :success
    analytics_30d = assigns(:analytics)

    # 30-day period should have same or more data than 7-day period
    assert analytics_30d[:overview][:total_badges_assigned] >=
             analytics_7d[:overview][:total_badges_assigned]
    assert analytics_30d[:overview][:total_badge_clicks] >=
             analytics_7d[:overview][:total_badge_clicks]
    assert analytics_30d[:overview][:total_badge_views] >=
             analytics_7d[:overview][:total_badge_views]
  end

  test 'badge type filtering affects analytics correctly' do
    sign_in_as_admin

    # Get analytics for all badge types
    get super_admin_badge_analytics_path
    assert_response :success
    all_analytics = assigns(:analytics)

    # Get analytics for specific badge type
    get super_admin_badge_analytics_path,
        params: {
          badge_type_id: @badge_type1.id,
        }
    assert_response :success
    filtered_analytics = assigns(:analytics)

    # Filtered analytics should have same or fewer metrics
    assert filtered_analytics[:overview][:total_badges_assigned] <=
             all_analytics[:overview][:total_badges_assigned]
    assert filtered_analytics[:overview][:total_badge_clicks] <=
             all_analytics[:overview][:total_badge_clicks]
    assert filtered_analytics[:overview][:total_badge_views] <=
             all_analytics[:overview][:total_badge_views]

    # Badge performance should only include the filtered badge type
    performance = filtered_analytics[:badge_performance]
    if performance.any?
      assert performance.all? { |p| p[:badge_type][:id] == @badge_type1.id }
    end
  end

  test 'error handling for invalid parameters' do
    sign_in_as_admin

    # Test with invalid date range
    get super_admin_badge_analytics_path, params: { date_range: 'invalid' }
    assert_response :success # Should handle gracefully

    # Test with invalid badge type ID
    get super_admin_badge_analytics_path, params: { badge_type_id: 'invalid' }
    assert_response :success # Should handle gracefully

    # Test with non-existent badge type ID
    get super_admin_badge_analytics_path, params: { badge_type_id: 99_999 }
    assert_response :success # Should handle gracefully
  end

  test 'performance and caching behavior' do
    sign_in_as_admin

    # First request should generate analytics
    start_time = Time.current
    get super_admin_badge_analytics_path
    first_request_time = Time.current - start_time
    assert_response :success

    # Second request should be faster due to caching
    start_time = Time.current
    get super_admin_badge_analytics_path
    second_request_time = Time.current - start_time
    assert_response :success

    # Note: In test environment, caching behavior might be different
    # This test mainly ensures no errors occur with caching
  end

  test 'responsive design elements are present' do
    sign_in_as_admin

    get super_admin_badge_analytics_path
    assert_response :success

    # Check for responsive classes (Tailwind CSS)
    assert_select '[class*="sm:"]' # Small screen responsive classes
    assert_select '[class*="md:"]' # Medium screen responsive classes
    assert_select '[class*="lg:"]' # Large screen responsive classes

    # Check for grid layouts
    assert_select '[class*="grid"]'
    assert_select '[class*="grid-cols"]'
  end

  test 'accessibility features are present' do
    sign_in_as_admin

    get super_admin_badge_analytics_path
    assert_response :success

    # Check for proper form labels
    assert_select 'label[for]'

    # Check for proper heading hierarchy
    assert_select 'h1'
    assert_select 'h2, h3, h4'

    # Check for proper table structure if tables are present
    assert_select 'table' do
      assert_select 'thead'
      assert_select 'tbody'
      assert_select 'th'
    end
  end

  private

  def create_comprehensive_test_data
    # Create badge assignments with different dates
    BadgeAssignment.create!(
      user: @user1,
      badge_type: @badge_type1,
      admin: @admin,
      assigned_at: 5.days.ago,
      notes: 'Expert level achievement',
    )

    BadgeAssignment.create!(
      user: @user2,
      badge_type: @badge_type2,
      admin: @admin,
      assigned_at: 3.days.ago,
      notes: 'Active contribution',
    )

    BadgeAssignment.create!(
      user: @user1,
      badge_type: @badge_type2,
      admin: @admin,
      assigned_at: 1.day.ago,
      notes: 'Additional contribution',
    )

    # Create badge clicks with different contexts and dates
    BadgeClick.create!(
      badge_type: @badge_type1,
      badge_owner: @user1,
      clicker_user: @user2,
      click_context: 'profile',
      ip_address: '***********',
      created_at: 4.days.ago,
    )

    BadgeClick.create!(
      badge_type: @badge_type1,
      badge_owner: @user1,
      clicker_user: nil,
      click_context: 'search_results',
      ip_address: '***********',
      created_at: 2.days.ago,
    )

    BadgeClick.create!(
      badge_type: @badge_type2,
      badge_owner: @user2,
      clicker_user: @user1,
      click_context: 'modal',
      ip_address: '***********',
      created_at: 1.day.ago,
    )

    # Create badge views with different patterns
    BadgeView.create!(
      viewed_user: @user1,
      viewer_user: @user2,
      badge_types_displayed: [@badge_type1.id],
      ip_address: '***********',
      created_at: 4.days.ago,
    )

    BadgeView.create!(
      viewed_user: @user2,
      viewer_user: nil,
      badge_types_displayed: [@badge_type2.id],
      ip_address: '***********',
      created_at: 2.days.ago,
    )

    BadgeView.create!(
      viewed_user: @user1,
      viewer_user: @user2,
      badge_types_displayed: [@badge_type1.id, @badge_type2.id],
      ip_address: '***********',
      created_at: 1.day.ago,
    )
  end

  def sign_in_as_admin
    post sign_in_path,
         params: {
           email: @admin.email,
           password: 'password123456',
         }
    follow_redirect!
  end
end
