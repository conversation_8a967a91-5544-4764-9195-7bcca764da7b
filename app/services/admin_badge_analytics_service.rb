# frozen_string_literal: true

class AdminBadgeAnalyticsService
  def self.generate_badge_analytics(date_range: 30.days, badge_type_id: nil)
    new.generate_badge_analytics(
      date_range: date_range,
      badge_type_id: badge_type_id,
    )
  end

  def initialize
    @cache_key_prefix = 'admin_badge_analytics'
  end

  def generate_badge_analytics(date_range: 30.days, badge_type_id: nil)
    cache_key = build_cache_key(date_range, badge_type_id)

    Rails
      .cache
      .fetch(cache_key, expires_in: 15.minutes) do
        {
          overview: calculate_overview_metrics(date_range, badge_type_id),
          badge_performance:
            calculate_badge_performance(date_range, badge_type_id),
          user_engagement:
            calculate_user_engagement_metrics(date_range, badge_type_id),
          click_analytics: calculate_click_analytics(date_range, badge_type_id),
          view_analytics: calculate_view_analytics(date_range, badge_type_id),
          trends: calculate_trends(date_range, badge_type_id),
          top_performers: calculate_top_performers(date_range, badge_type_id),
        }
      end
  end

  private

  def build_cache_key(date_range, badge_type_id)
    range_key = "#{date_range.to_i}s"
    badge_key = badge_type_id ? "badge_#{badge_type_id}" : 'all_badges'
    "#{@cache_key_prefix}_#{range_key}_#{badge_key}"
  end

  def calculate_overview_metrics(date_range, badge_type_id)
    start_date = date_range.ago

    # Base queries with date filtering
    badge_assignments_scope =
      BadgeAssignment.where('badge_assignments.created_at >= ?', start_date)
    badge_clicks_scope =
      BadgeClick.where('badge_clicks.created_at >= ?', start_date)
    badge_views_scope =
      BadgeView.where('badge_views.created_at >= ?', start_date)

    # Apply badge type filtering if specified
    if badge_type_id
      badge_assignments_scope =
        badge_assignments_scope.where(badge_type_id: badge_type_id)
      badge_clicks_scope =
        badge_clicks_scope.where(badge_type_id: badge_type_id)
      badge_views_scope = badge_views_scope.with_badge_type(badge_type_id)
    end

    {
      total_badges_assigned: badge_assignments_scope.count,
      total_badge_clicks: badge_clicks_scope.count,
      total_badge_views: badge_views_scope.count,
      unique_badge_clickers: calculate_unique_clickers(badge_clicks_scope),
      unique_badge_viewers: calculate_unique_viewers(badge_views_scope),
      active_badge_types:
        calculate_active_badge_types(start_date, badge_type_id),
      users_with_badges: calculate_users_with_badges(start_date, badge_type_id),
      avg_clicks_per_badge:
        calculate_avg_clicks_per_badge(
          badge_clicks_scope,
          badge_assignments_scope,
        ),
      avg_views_per_badge:
        calculate_avg_views_per_badge(
          badge_views_scope,
          badge_assignments_scope,
        ),
    }
  end

  def calculate_badge_performance(date_range, badge_type_id)
    start_date = date_range.ago

    # Get badge types to analyze
    badge_types =
      badge_type_id ? BadgeType.where(id: badge_type_id) : BadgeType.active

    badge_types
      .map do |badge_type|
        clicks =
          BadgeClick
            .where(badge_type: badge_type)
            .where('badge_clicks.created_at >= ?', start_date)
        views =
          BadgeView
            .with_badge_type(badge_type.id)
            .where('badge_views.created_at >= ?', start_date)
        assignments =
          BadgeAssignment
            .where(badge_type: badge_type)
            .where('badge_assignments.created_at >= ?', start_date)

        {
          badge_type: {
            id: badge_type.id,
            name: badge_type.name,
            description: badge_type.description,
            background_color: badge_type.background_color,
            text_color: badge_type.text_color,
            icon: badge_type.icon,
          },
          metrics: {
            total_assignments: assignments.count,
            total_clicks: clicks.count,
            total_views: views.count,
            unique_clickers: calculate_unique_clickers(clicks),
            unique_viewers: calculate_unique_viewers(views),
            click_through_rate:
              calculate_click_through_rate(clicks.count, views.count),
            avg_clicks_per_assignment:
              if assignments.count > 0
                (clicks.count.to_f / assignments.count).round(2)
              else
                0
              end,
            avg_views_per_assignment:
              if assignments.count > 0
                (views.count.to_f / assignments.count).round(2)
              else
                0
              end,
          },
        }
      end
      .sort_by { |badge| -badge[:metrics][:total_clicks] }
  end

  def calculate_user_engagement_metrics(date_range, badge_type_id)
    start_date = date_range.ago

    # Users with badges in the specified period
    users_with_badges =
      if badge_type_id
        User
          .joins(:badge_assignments)
          .where(badge_assignments: { badge_type_id: badge_type_id })
          .where('badge_assignments.created_at >= ?', start_date)
          .distinct
      else
        User
          .joins(:badge_assignments)
          .where('badge_assignments.created_at >= ?', start_date)
          .distinct
      end

    total_users_with_badges = users_with_badges.count

    # Calculate engagement metrics
    users_with_clicks =
      users_with_badges
        .joins(:badge_clicks_received)
        .where('badge_clicks.created_at >= ?', start_date)
        .distinct
        .count

    users_with_views =
      users_with_badges
        .joins(:badge_views_received)
        .where('badge_views.created_at >= ?', start_date)
        .distinct
        .count

    {
      total_users_with_badges: total_users_with_badges,
      users_with_badge_clicks: users_with_clicks,
      users_with_badge_views: users_with_views,
      click_engagement_rate:
        if total_users_with_badges > 0
          (users_with_clicks.to_f / total_users_with_badges * 100).round(2)
        else
          0
        end,
      view_engagement_rate:
        if total_users_with_badges > 0
          (users_with_views.to_f / total_users_with_badges * 100).round(2)
        else
          0
        end,
      avg_badges_per_user:
        calculate_avg_badges_per_user(start_date, badge_type_id),
    }
  end

  def calculate_click_analytics(date_range, badge_type_id)
    start_date = date_range.ago

    clicks_scope = BadgeClick.where('badge_clicks.created_at >= ?', start_date)
    clicks_scope =
      clicks_scope.where(badge_type_id: badge_type_id) if badge_type_id

    {
      total_clicks: clicks_scope.count,
      clicks_by_context: clicks_scope.group(:click_context).count,
      clicks_by_day: calculate_daily_counts(clicks_scope, date_range),
      authenticated_clicks: clicks_scope.authenticated_clicks.count,
      anonymous_clicks: clicks_scope.anonymous_clicks.count,
      top_clicked_badges: calculate_top_clicked_badges(clicks_scope),
      peak_click_hours: calculate_peak_hours(clicks_scope),
    }
  end

  def calculate_view_analytics(date_range, badge_type_id)
    start_date = date_range.ago

    views_scope = BadgeView.where('badge_views.created_at >= ?', start_date)
    views_scope = views_scope.with_badge_type(badge_type_id) if badge_type_id

    {
      total_views: views_scope.count,
      views_by_day: calculate_daily_counts(views_scope, date_range),
      authenticated_views: views_scope.authenticated_views.count,
      anonymous_views: views_scope.anonymous_views.count,
      top_viewed_users: calculate_top_viewed_users(views_scope),
      peak_view_hours: calculate_peak_hours(views_scope),
    }
  end

  def calculate_trends(date_range, badge_type_id)
    # Calculate week-over-week trends
    current_period_start = date_range.ago
    previous_period_start = (date_range * 2).ago
    previous_period_end = date_range.ago

    # Badge assignments
    current_assignments =
      BadgeAssignment.where(
        'badge_assignments.created_at >= ?',
        current_period_start,
      )
    previous_assignments =
      BadgeAssignment.where(
        'badge_assignments.created_at BETWEEN ? AND ?',
        previous_period_start,
        previous_period_end,
      )

    current_clicks =
      BadgeClick.where('badge_clicks.created_at >= ?', current_period_start)
    previous_clicks =
      BadgeClick.where(
        'badge_clicks.created_at BETWEEN ? AND ?',
        previous_period_start,
        previous_period_end,
      )

    current_views =
      BadgeView.where('badge_views.created_at >= ?', current_period_start)
    previous_views =
      BadgeView.where(
        'badge_views.created_at BETWEEN ? AND ?',
        previous_period_start,
        previous_period_end,
      )

    if badge_type_id
      current_assignments =
        current_assignments.where(badge_type_id: badge_type_id)
      previous_assignments =
        previous_assignments.where(badge_type_id: badge_type_id)
      current_clicks = current_clicks.where(badge_type_id: badge_type_id)
      previous_clicks = previous_clicks.where(badge_type_id: badge_type_id)
      current_views = current_views.with_badge_type(badge_type_id)
      previous_views = previous_views.with_badge_type(badge_type_id)
    end

    current_assignments_count = current_assignments.count
    previous_assignments_count = previous_assignments.count
    current_clicks_count = current_clicks.count
    previous_clicks_count = previous_clicks.count
    current_views_count = current_views.count
    previous_views_count = previous_views.count

    # Calculate engagement rates
    current_engagement_rate =
      if current_assignments_count > 0
        (current_clicks_count.to_f / current_assignments_count * 100).round(2)
      else
        0
      end
    previous_engagement_rate =
      if previous_assignments_count > 0
        (previous_clicks_count.to_f / previous_assignments_count * 100).round(2)
      else
        0
      end

    {
      assignments_change:
        calculate_percentage_change(
          current_assignments_count,
          previous_assignments_count,
        ),
      clicks_change:
        calculate_percentage_change(
          current_clicks_count,
          previous_clicks_count,
        ),
      views_change:
        calculate_percentage_change(current_views_count, previous_views_count),
      engagement_change:
        calculate_percentage_change(
          current_engagement_rate,
          previous_engagement_rate,
        ),
    }
  end

  def calculate_top_performers(date_range, badge_type_id)
    start_date = date_range.ago

    # Top users by badge clicks received
    top_clicked_users =
      User
        .joins(:badge_clicks_received)
        .where('badge_clicks.created_at >= ?', start_date)
        .group('users.id, users.first_name, users.last_name, users.email')
        .order('COUNT(badge_clicks.id) DESC')
        .limit(10)
        .pluck(
          'users.id, users.first_name, users.last_name, users.email, COUNT(badge_clicks.id)',
        )
        .map do |id, first_name, last_name, email, count|
          full_name =
            [first_name, last_name].compact.join(' ').presence || 'Unknown'
          { id: id, name: full_name, email: email, clicks: count }
        end

    # Top users by badge views received
    top_viewed_users =
      User
        .joins(:badge_views_received)
        .where('badge_views.created_at >= ?', start_date)
        .group('users.id, users.first_name, users.last_name, users.email')
        .order('COUNT(badge_views.id) DESC')
        .limit(10)
        .pluck(
          'users.id, users.first_name, users.last_name, users.email, COUNT(badge_views.id)',
        )
        .map do |id, first_name, last_name, email, count|
          full_name =
            [first_name, last_name].compact.join(' ').presence || 'Unknown'
          { id: id, name: full_name, email: email, views: count }
        end

    { top_clicked_users: top_clicked_users, top_viewed_users: top_viewed_users }
  end

  # Helper methods
  def calculate_unique_clickers(clicks_scope)
    authenticated_count =
      clicks_scope
        .where.not(clicker_user_id: nil)
        .distinct
        .count(:clicker_user_id)
    anonymous_count =
      clicks_scope.where(clicker_user_id: nil).distinct.count(:ip_address)
    authenticated_count + anonymous_count
  end

  def calculate_unique_viewers(views_scope)
    authenticated_count =
      views_scope.where.not(viewer_user_id: nil).distinct.count(:viewer_user_id)
    anonymous_count =
      views_scope.where(viewer_user_id: nil).distinct.count(:ip_address)
    authenticated_count + anonymous_count
  end

  def calculate_active_badge_types(start_date, badge_type_id)
    if badge_type_id
      1
    else
      BadgeAssignment
        .where('badge_assignments.created_at >= ?', start_date)
        .distinct
        .count(:badge_type_id)
    end
  end

  def calculate_users_with_badges(start_date, badge_type_id)
    scope =
      BadgeAssignment.where('badge_assignments.created_at >= ?', start_date)
    scope = scope.where(badge_type_id: badge_type_id) if badge_type_id
    scope.distinct.count(:user_id)
  end

  def calculate_avg_clicks_per_badge(clicks_scope, assignments_scope)
    assignments_count = assignments_scope.count
    if assignments_count > 0
      (clicks_scope.count.to_f / assignments_count).round(2)
    else
      0
    end
  end

  def calculate_avg_views_per_badge(views_scope, assignments_scope)
    assignments_count = assignments_scope.count
    if assignments_count > 0
      (views_scope.count.to_f / assignments_count).round(2)
    else
      0
    end
  end

  def calculate_click_through_rate(clicks_count, views_count)
    views_count > 0 ? (clicks_count.to_f / views_count * 100).round(2) : 0
  end

  def calculate_avg_badges_per_user(start_date, badge_type_id)
    scope =
      BadgeAssignment.where('badge_assignments.created_at >= ?', start_date)
    scope = scope.where(badge_type_id: badge_type_id) if badge_type_id

    total_assignments = scope.count
    unique_users = scope.distinct.count(:user_id)

    unique_users > 0 ? (total_assignments.to_f / unique_users).round(2) : 0
  end

  def calculate_top_clicked_badges(clicks_scope)
    clicks_scope
      .joins(:badge_type)
      .group('badge_types.id, badge_types.name')
      .order('COUNT(badge_clicks.id) DESC')
      .limit(5)
      .pluck('badge_types.id, badge_types.name, COUNT(badge_clicks.id)')
      .map { |id, name, count| { id: id, name: name, clicks: count } }
  end

  def calculate_top_viewed_users(views_scope)
    views_scope
      .joins(:viewed_user)
      .group('users.id, users.first_name, users.last_name, users.email')
      .order('COUNT(badge_views.id) DESC')
      .limit(5)
      .pluck(
        'users.id, users.first_name, users.last_name, users.email, COUNT(badge_views.id)',
      )
      .map do |id, first_name, last_name, email, count|
        full_name =
          [first_name, last_name].compact.join(' ').presence || 'Unknown'
        { id: id, name: full_name, email: email, views: count }
      end
  end

  def calculate_peak_hours(scope)
    scope
      .group('EXTRACT(hour FROM created_at)')
      .count
      .sort_by { |hour, count| -count }
      .first(3)
      .map { |hour, count| { hour: hour.to_i, count: count } }
  end

  def calculate_daily_counts(scope, date_range)
    # Simple daily grouping without Groupdate gem
    start_date = date_range.ago.beginning_of_day
    end_date = Time.current.end_of_day

    # Get actual data grouped by date
    daily_data = scope.group('DATE(created_at)').count

    # Fill in missing dates with 0 counts
    result = {}
    current_date = start_date.to_date

    while current_date <= end_date.to_date
      date_key = current_date.to_s
      result[date_key] = daily_data[date_key] || 0
      current_date += 1.day
    end

    result
  end

  def calculate_percentage_change(current, previous)
    return 100 if current > 0 && previous.zero?
    return 0 if previous.zero?

    ((current - previous).to_f / previous * 100).round(2)
  end
end
